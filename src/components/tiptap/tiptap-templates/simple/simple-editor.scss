
.tiptap.ProseMirror {
  font-family: "DM Sans", sans-serif;
}

.content-wrapper {
  height: calc(100% - var(--tt-toolbar-height));
  overflow-y: auto;

  &::-webkit-scrollbar {
    display: block;
    width: 0.5rem;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--tt-scrollbar-color);
    border-radius: 4px;
  }

  /* Firefox scrollbar */
  scrollbar-width: thin;
  scrollbar-color: var(--tt-scrollbar-color) transparent;
}

.simple-editor-content {
  width: 100%;
  margin: 0 auto;
}

.simple-editor-content .tiptap.ProseMirror {
  padding: 0 0 3rem 0;
}

@media screen and (max-width: 480px) {
  .simple-editor-content .tiptap.ProseMirror {
    padding: 0 0 1.5rem 0;
  }
}

.tiptap-editor-container {
	position: relative;
	width: 100%;

	&.error {
		.simple-editor-content {
			border-color: #f44336;
		}
	}
}

.simple-editor-content {
	position: relative;
	background-color: var(--tt-bg-color);
	border: 1px solid rgba(0, 0, 0, 0.23);
	border-radius: 4px;
	padding: 16px;
	min-height: 200px;
	transition: border-color 0.2s ease;

	&:hover {
		border-color: rgba(0, 0, 0, 0.87);
	}

	&:focus-within {
		border-color: #1976d2;
		border-width: 2px;
		padding: 15px;
	}
}

.tiptap-error-message {
	color: #f44336;
	font-size: 0.75rem;
	margin-top: 3px;
	margin-left: 14px;
}
