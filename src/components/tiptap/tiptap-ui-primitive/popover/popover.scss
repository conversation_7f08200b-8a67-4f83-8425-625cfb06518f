.tiptap-popover {
  --tt-popover-bg-color: var(--white);
  --tt-popover-border-color: var(--tt-gray-light-a-100);
  --tt-popover-text-color: var(--tt-gray-light-a-600);

  .dark & {
    --tt-popover-border-color: var(--tt-gray-dark-a-50);
    --tt-popover-bg-color: var(--tt-gray-dark-50);
    --tt-popover-text-color: var(--tt-gray-dark-a-600);
  }
}

/* --------------------------------------------
    --------- POPOVER STYLING SETTINGS -----------
    -------------------------------------------- */
.tiptap-popover {
  --padding: 0.25rem;
  --border-width: 1px;

  z-index: 50;
  border-radius: calc(
    var(--padding) + var(--tt-radius-lg) + var(--border-width)
  );
  border: var(--border-width) solid var(--tt-popover-border-color);
  background-color: var(--tt-popover-bg-color);
  padding: var(--padding);
  color: var(--tt-popover-text-color);
  box-shadow: var(--tt-shadow-elevated-md);
  outline: none;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 0.25rem;

  button {
    width: 100%;
  }

  &[data-orientation="horizontal"] {
    --padding: 0.125rem;
  }

  /* Animation states */
  &[data-state="open"] {
    animation:
      fadeIn 150ms cubic-bezier(0.16, 1, 0.3, 1),
      zoomIn 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  &[data-state="closed"] {
    animation:
      fadeOut 150ms cubic-bezier(0.16, 1, 0.3, 1),
      zoomOut 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  /* Position-based animations */
  &[data-side="top"],
  &[data-side="top-start"],
  &[data-side="top-end"] {
    animation: slideFromBottom 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  &[data-side="right"],
  &[data-side="right-start"],
  &[data-side="right-end"] {
    animation: slideFromLeft 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  &[data-side="bottom"],
  &[data-side="bottom-start"],
  &[data-side="bottom-end"] {
    animation: slideFromTop 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  &[data-side="left"],
  &[data-side="left-start"],
  &[data-side="left-end"] {
    animation: slideFromRight 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }
}
