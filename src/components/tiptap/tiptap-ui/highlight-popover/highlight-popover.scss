:root {
  --tt-highlight-green: #dcfce7;
  --tt-highlight-blue: #e0f2fe;
  --tt-highlight-red: #ffe4e6;
  --tt-highlight-purple: #f3e8ff;
  --tt-highlight-yellow: #fef9c3;
}

.dark {
  --tt-highlight-green: #509568;
  --tt-highlight-blue: #6e92aa;
  --tt-highlight-red: #743e42;
  --tt-highlight-purple: #583e74;
  --tt-highlight-yellow: #6b6524;
}

.tiptap-highlight-content {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  outline: none;
}

.tiptap-button-highlight {
  position: relative;
  width: 1.25rem;
  height: 1.25rem;
  margin: 0 -0.175rem;
  border-radius: 100%;
  background-color: var(--highlight-color);
  transition: transform 0.2s ease;

  &::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    border-radius: inherit;
    box-sizing: border-box;
    border: 1px solid var(--highlight-color);
    filter: brightness(95%);
    mix-blend-mode: multiply;

    .dark & {
      filter: brightness(140%);
      mix-blend-mode: lighten;
    }
  }
}

.tiptap-button {
  &[data-active-state="on"] {
    .tiptap-button-highlight {
      &::after {
        filter: brightness(80%);
      }
    }
  }

  .dark & {
    &[data-active-state="on"] {
      .tiptap-button-highlight {
        &::after {
          // Andere Eigenschaft für .dark Kontext
          filter: brightness(180%);
        }
      }
    }
  }
}
